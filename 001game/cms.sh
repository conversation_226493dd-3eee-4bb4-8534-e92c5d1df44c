#!/bin/bash

FILE="cmsd"
PEM="$HOME/Documents/company/001/001_zzn.pem"
PROJECT_DIR="$HOME/lang/go/src/coding/playmates/gamebox-server/uni-server/cms-server"
REMOTE_DIR="/data/server/cms-server" # 服务器上的目录

echo "正在生成本地 $FILE 可执行文件..."
sh "$PROJECT_DIR"/build/linux_publish.sh
echo "生成本地 $FILE 可执行文件成功"

function handle_error {
  if [ $? -ne 0 ]; then
    echo "Error: \$1"
    exit 1
  fi
}

function connect_server {
  ssh -T -i "$PEM" root@175.178.230.199 -p 2222
  handle_error "Failed to execute command over ssh"
}

function scp_to_server {
  scp -T -i "$PEM" -P 2222 "$1" root@175.178.230.199:"$2"
  handle_error "Failed to copy \$1 to \$2 on the server"
}

connect_server << 'EOF'
FILE="cmsd" # 可执行文件名
date=$(date +%m%d)  # 获取当前日期
filename="${FILE}_${date}_bak"  # 备份文件名
new_filename="" # 新备份文件名
REMOTE_DIR="/data/server/cms-server" # 服务器上的目录
index=0

echo "正在备份服务器上的 $FILE 可执行文件..."
cd $REMOTE_DIR || exit

if [[ ! -e $filename ]]; then
    new_filename=$filename
fi

if [[ $new_filename == "" ]]; then
    while [[ -e $filename && $index -lt 20 ]]
    do
        index=$((index+1))
        filename="${FILE}_${date}_${index}_bak"
        new_filename=$filename
    done
fi

if [[ $new_filename == "" ]]; then
    echo "备份文件失败，备份文件数量已达上限"
    exit 1
fi

mv $FILE "$new_filename"
echo "备份服务器上的 $FILE 为 $new_filename 成功"
EOF

echo "正在上传 $FILE 可执行文件到服务器..."
scp_to_server "$PROJECT_DIR"/build/release/linux/cmsd "$REMOTE_DIR/"
echo "上传 $FILE 可执行文件到服务器成功"

connect_server << 'EOF'
FILE="cmsd"
REMOTE_DIR="/data/server/cms-server" # 服务器上的目录
REMOTE_BIN_DIR="$REMOTE_DIR/bin" # 服务器上的可执行文件目录

echo "正在重启服务器上的 $FILE 服务..."
cd $REMOTE_BIN_DIR || exit
sh upgrade.sh
echo "重启服务器上的 $FILE 服务成功"
EOF