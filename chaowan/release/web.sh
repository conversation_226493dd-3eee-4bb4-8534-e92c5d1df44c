#!/bin/bash

PEM="$HOME/Documents/company/潮玩NEO/zch.pem"
PROJECT_DIR="$HOME/lang/vue/playmates/BlindboxCms"
DIST_DIR_NAME="dist"
REMOTE_WEB_DIR_NAME="mb_cms_web"
REMOTE_BASE_DIR="/data/html"

# 构建
cd $PROJECT_DIR
source ~/.nvm/nvm.sh && nvm use 14

echo "========== 正在构建... =========="
if npm run build:prod; then
    echo "========== 构建完成 =========="
else
    echo "========== 构建失败 =========="
    exit 1
fi

# 打包 tgz
# 这里使用 gtar 打包，否则在 linux 上解压时会产生一些GNU tar无法识别的额外的信息
# Mac OS X 使用的是BSD tar
echo "========== 正在打包 tgz 文件... =========="
# 使用 tar 命令创建一个名为 ${DIST_DIR_NAME}.tgz 的压缩文件
# -c: 创建新的归档文件
# -z: 使用 gzip 压缩
# -v: 显示详细信息
# -f: 指定归档文件名
# ${DIST_DIR_NAME}.tgz: 输出的压缩文件名
# ${DIST_DIR_NAME}: 要压缩的目录
gtar -czvf ${DIST_DIR_NAME}.tgz ${DIST_DIR_NAME}
echo "========== 打包 tgz 文件完成 =========="


function handle_error {
  if [ $? -ne 0 ]; then
    echo "Error: \$1"
    exit 1
  fi
}

function connect_server {
  ssh -T -i "$PEM" root@************ -p 2222
  handle_error "Failed to execute command over ssh"
}

function scp_to_server {
  scp -T -i "$PEM" -P 2222 "$1" root@************:"$2"
  handle_error "Failed to copy \$1 to \$2 on the server"
}

# 上传并备份
echo "========== 正在上传... =========="
scp_to_server "$PROJECT_DIR"/${DIST_DIR_NAME}.tgz "$REMOTE_BASE_DIR/"
echo "========== 上传完成 =========="

echo "========== 删除本地 tgz 文件... =========="
rm -rf ${DIST_DIR_NAME}.tgz
echo "========== 删除本地 tgz 文件完成 =========="

connect_server << 'EOF'
DIST_DIR_NAME="dist"
REMOTE_WEB_DIR_NAME="mb_cms_web"
REMOTE_BASE_DIR="/data/html"

echo "========== 正在备份... =========="
cd $REMOTE_BASE_DIR
rm -rf ${REMOTE_WEB_DIR_NAME}_bak
mv ${REMOTE_WEB_DIR_NAME} ${REMOTE_WEB_DIR_NAME}_bak
echo "========== 备份完成 =========="

echo "========== 正在解压... =========="
mkdir -p ${REMOTE_WEB_DIR_NAME}
# --strip-components=1 用于在解压时去掉压缩包内顶层目录
# 这样可以直接将压缩包内容解压到目标目录，而不会创建额外的子目录
tar -xvf ${DIST_DIR_NAME}.tgz -C ${REMOTE_WEB_DIR_NAME} --strip-components=1
echo "========== 解压完成 =========="
EOF
