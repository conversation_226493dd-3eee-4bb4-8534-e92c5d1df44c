#!/bin/bash

# 定义颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查 sshpass 是否已安装
if ! command -v sshpass &> /dev/null; then
    printf "${RED}错误: sshpass 未安装${NC}\n"
    printf "请运行以下命令安装 sshpass:\n"
    printf "  brew install esolitos/ipa/sshpass\n"
    exit 1
fi

# 定义变量
REMOTE_HOST="**************"
REMOTE_USER="root"
REMOTE_PASS="bNAqne8nfSU6DM"
# 如果环境变量 GOPROJECT_PATH 存在就使用它，否则使用默认值
GO_PATH=${GOPROJECT_PATH:-"/Users/<USER>/lang/go/src/coding/daao/gin-vue-admin/server"}
REMOTE_PATH="/www/wwwroot/douyin-bg4"
LOCAL_BINARY_NAME="server"

# 使用策略模式：分离不同类型的日志
DEPLOY_LOG="${REMOTE_PATH}/api/deploy.log"      # 部署操作日志（纯文本）
APP_LOG="${REMOTE_PATH}/api/app.log"            # 应用运行日志（可能包含二进制）
ERROR_LOG="${REMOTE_PATH}/api/error.log"        # 错误日志

# 统一的日志记录函数
log_deploy() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[${timestamp}] ${message}"
}

# 策略模式：文件完整性验证策略
# 接口：验证文件完整性
validate_file_integrity() {
    local local_file_path="$1"
    local remote_file_path="$2"
    
    # 获取本地文件大小
    local local_size=$(stat -f%z "$local_file_path" 2>/dev/null || stat -c%s "$local_file_path" 2>/dev/null)
    if [ -z "$local_size" ]; then
        printf "${RED}错误: 无法获取本地文件大小${NC}\n"
        return 1
    fi
    
    printf "${YELLOW}本地文件大小: ${local_size} bytes${NC}\n"
    
    # 获取远程文件大小（去掉export命令）
    local remote_size=$(sshpass -p "${REMOTE_PASS}" ssh "${REMOTE_USER}@${REMOTE_HOST}" \
        "stat -c%s '${remote_file_path}' 2>/dev/null || echo 'ERROR'")
    
    if [ "$remote_size" = "ERROR" ] || [ -z "$remote_size" ]; then
        printf "${RED}错误: 无法获取远程文件大小${NC}\n"
        return 1
    fi
    
    printf "${YELLOW}远程文件大小: ${remote_size} bytes${NC}\n"
    
    # 比较文件大小
    if [ "$local_size" != "$remote_size" ]; then
        printf "${RED}错误: 文件大小不匹配! 本地: ${local_size}, 远程: ${remote_size}${NC}\n"
        printf "${RED}上传可能不完整，停止部署以保护服务安全${NC}\n"
        return 1
    fi
    
    printf "${GREEN}✓ 文件完整性验证通过${NC}\n"
    return 0
}

# 模板方法模式：部署流程模板
# 抽象步骤1：构建程序
build_program() {
    printf "${GREEN}开始构建go程序...${NC}\n"
    
    # 切换到Go项目目录
    cd ${GO_PATH} || {
        printf "${RED}切换目录失败: ${GO_PATH}${NC}\n"
        return 1
    }
    printf "✓ 已切换到项目目录\n"
    
    # 构建Go程序
    printf "正在构建Go程序...\n"
    GOOS=linux GOARCH=amd64 go build . || {
        printf "${RED}Go程序构建失败${NC}\n"
        return 1
    }
    printf "${GREEN}✓ Go程序构建成功${NC}\n"
    return 0
}

# 抽象步骤2：备份和上传
backup_and_upload() {
    printf "正在备份并上传文件到服务器...\n"
    
    # 备份现有文件并初始化日志系统
    sshpass -p "${REMOTE_PASS}" ssh "${REMOTE_USER}@${REMOTE_HOST}" "
        # 设置环境变量，确保干净的文本输出 
        # 确保目录存在
        mkdir -p ${REMOTE_PATH}/api
        
        # 完全重新初始化日志文件（删除任何现有内容）
        rm -f ${DEPLOY_LOG} ${APP_LOG} ${ERROR_LOG}
        
        # 创建全新的日志文件
        printf '===========================================\\n' > ${DEPLOY_LOG}
        printf '[%s] 开始新的部署\\n' \"\$(date '+%Y-%m-%d %H:%M:%S')\" >> ${DEPLOY_LOG}
        printf '[%s] 部署脚本版本: \\n' \"\$(date '+%Y-%m-%d %H:%M:%S')\" >> ${DEPLOY_LOG}
        
        # 初始化应用日志和错误日志文件
        printf '===========================================\\n' > ${APP_LOG}
        printf '[%s] 应用日志启动\\n' \"\$(date '+%Y-%m-%d %H:%M:%S')\" >> ${APP_LOG}
        
        printf '===========================================\\n' > ${ERROR_LOG}
        printf '[%s] 错误日志启动\\n' \"\$(date '+%Y-%m-%d %H:%M:%S')\" >> ${ERROR_LOG}
        
        # 记录初始化完成
        printf '[%s] 已初始化所有日志文件\\n' \"\$(date '+%Y-%m-%d %H:%M:%S')\" >> ${DEPLOY_LOG}
        printf '[%s] 开始检查目录和文件...\\n' \"\$(date '+%Y-%m-%d %H:%M:%S')\" >> ${DEPLOY_LOG}
        
        # 检查并显示当前文件状态（避免特殊字符）
        if [ -f ${REMOTE_PATH}/api/server ]; then
            printf '[%s] 找到现有server文件\\n' \"\$(date '+%Y-%m-%d %H:%M:%S')\" >> ${DEPLOY_LOG}
            # 使用 stat 命令代替 ls -l，避免颜色编码和特殊字符
            file_size=\$(stat -c%s ${REMOTE_PATH}/api/server 2>/dev/null || echo '未知')
            file_time=\$(stat -c%y ${REMOTE_PATH}/api/server 2>/dev/null | cut -d' ' -f1,2 || echo '未知')
            printf '[%s] 现有文件大小: %s bytes\\n' \"\$(date '+%Y-%m-%d %H:%M:%S')\" \"\${file_size}\" >> ${DEPLOY_LOG}
            printf '[%s] 现有文件时间: %s\\n' \"\$(date '+%Y-%m-%d %H:%M:%S')\" \"\${file_time}\" >> ${DEPLOY_LOG}
        else
            printf '[%s] 未找到现有server文件\\n' \"\$(date '+%Y-%m-%d %H:%M:%S')\" >> ${DEPLOY_LOG}
        fi
        
        # 备份当前server文件
        if [ -f ${REMOTE_PATH}/api/server ]; then
            printf '[%s] 准备备份现有server文件...\\n' \"\$(date '+%Y-%m-%d %H:%M:%S')\" >> ${DEPLOY_LOG}
            # 如果存在旧的备份文件，先删除
            [ -f ${REMOTE_PATH}/api/server_bak ] && rm ${REMOTE_PATH}/api/server_bak
            # 创建新的备份
            cp ${REMOTE_PATH}/api/server ${REMOTE_PATH}/api/server_bak
            printf '[%s] 已备份原服务文件\\n' \"\$(date '+%Y-%m-%d %H:%M:%S')\" >> ${DEPLOY_LOG}
        fi
    " || {
        printf "${RED}备份操作失败${NC}\n"
        return 1
    }
    
    # 上传新文件（带进度显示）
    printf "${GREEN}正在上传新文件...${NC}\n"
    
    # 获取文件大小用于进度计算
    local file_size=$(stat -f%z server 2>/dev/null || stat -c%s server 2>/dev/null)
    printf "${YELLOW}文件大小: $(printf "%'d" $file_size) bytes ($(echo "scale=2; $file_size/1024/1024" | bc 2>/dev/null || echo "N/A") MB)${NC}\n"
    
    # 检查是否有 rsync 命令（优先使用，进度显示更好）
    if command -v rsync &> /dev/null; then
        printf "${YELLOW}使用 rsync 上传（更好的进度显示）...${NC}\n"
        sshpass -p "${REMOTE_PASS}" rsync -avz --progress server "${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH}/api/server_new" || {
            printf "${RED}rsync 上传失败，尝试使用 scp...${NC}\n"
            # 回退到 scp
            sshpass -p "${REMOTE_PASS}" scp server "${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH}/api/server_new" || {
                printf "${RED}文件上传失败${NC}\n"
                return 1
            }
        }
    elif command -v pv &> /dev/null; then
        # 如果有 pv 命令，使用它来显示进度
        printf "${YELLOW}使用 pv 显示上传进度...${NC}\n"
        pv server | sshpass -p "${REMOTE_PASS}" ssh "${REMOTE_USER}@${REMOTE_HOST}" "cat > ${REMOTE_PATH}/api/server_new" || {
            printf "${RED}pv 上传失败，尝试使用 scp...${NC}\n"
            # 回退到 scp
            sshpass -p "${REMOTE_PASS}" scp server "${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH}/api/server_new" || {
                printf "${RED}文件上传失败${NC}\n"
                return 1
            }
        }
    else
        # 使用 scp（不带 -q 参数以显示基本进度）
        printf "${YELLOW}使用 scp 上传...${NC}\n"
        sshpass -p "${REMOTE_PASS}" scp server "${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH}/api/server_new" || {
            printf "${RED}文件上传失败${NC}\n"
            return 1
        }
    fi
    
    printf "${GREEN}✓ 文件上传完成${NC}\n"
    
    return 0
}

# 抽象步骤3：验证并部署
validate_and_deploy() {
    local local_file="${GO_PATH}/${LOCAL_BINARY_NAME}"
    local remote_file="${REMOTE_PATH}/api/server_new"
    
    # 验证文件完整性
    if ! validate_file_integrity "$local_file" "$remote_file"; then
        printf "${RED}文件完整性验证失败，回滚操作...${NC}\n"
        
        # 清理不完整的文件
        sshpass -p "${REMOTE_PASS}" ssh "${REMOTE_USER}@${REMOTE_HOST}" "
            echo \"[\$(date '+%Y-%m-%d %H:%M:%S')] 文件完整性验证失败，清理不完整的文件\" >> ${DEPLOY_LOG}
            rm -f ${REMOTE_PATH}/api/server_new
            echo \"[\$(date '+%Y-%m-%d %H:%M:%S')] 已清理不完整的文件\" >> ${DEPLOY_LOG}
        "
        
        return 1
    fi
    
    # 原子性替换文件
    sshpass -p "${REMOTE_PASS}" ssh "${REMOTE_USER}@${REMOTE_HOST}" "
        echo \"[\$(date '+%Y-%m-%d %H:%M:%S')] 文件完整性验证通过，开始原子性替换\" >> ${DEPLOY_LOG}
        
        # 原子性操作：重命名替换
        mv ${REMOTE_PATH}/api/server_new ${REMOTE_PATH}/api/server
        
        # 添加执行权限
        chmod +x ${REMOTE_PATH}/api/server
        
        # 验证文件完整性
        if [ -f ${REMOTE_PATH}/api/server ] && [ -x ${REMOTE_PATH}/api/server ]; then
            echo \"[\$(date '+%Y-%m-%d %H:%M:%S')] 新文件部署成功\" >> ${DEPLOY_LOG}
            
            # 记录最终文件信息（避免特殊字符）
            file_size=\$(stat -c%s ${REMOTE_PATH}/api/server 2>/dev/null || echo '未知')
            file_time=\$(stat -c%y ${REMOTE_PATH}/api/server 2>/dev/null | cut -d' ' -f1,2 || echo '未知')
            file_perm=\$(stat -c%a ${REMOTE_PATH}/api/server 2>/dev/null || echo '未知')
            file_md5=\$(md5sum ${REMOTE_PATH}/api/server 2>/dev/null | cut -d' ' -f1 || echo '未知')
            echo \"[\$(date '+%Y-%m-%d %H:%M:%S')] 最终文件大小: \${file_size} bytes\" >> ${DEPLOY_LOG}
            echo \"[\$(date '+%Y-%m-%d %H:%M:%S')] 最终文件时间: \${file_time}\" >> ${DEPLOY_LOG}
            echo \"[\$(date '+%Y-%m-%d %H:%M:%S')] 最终文件权限: \${file_perm}\" >> ${DEPLOY_LOG}
            echo \"[\$(date '+%Y-%m-%d %H:%M:%S')] 文件MD5校验: \${file_md5}\" >> ${DEPLOY_LOG}
            echo \"[\$(date '+%Y-%m-%d %H:%M:%S')] 部署完成时间: \$(date '+%Y-%m-%d %H:%M:%S')\" >> ${DEPLOY_LOG}
        else
            echo \"[\$(date '+%Y-%m-%d %H:%M:%S')] 警告：新文件部署失败\" >> ${DEPLOY_LOG}
            # 如果失败，尝试恢复备份
            if [ -f ${REMOTE_PATH}/api/server_bak ]; then
                echo \"[\$(date '+%Y-%m-%d %H:%M:%S')] 正在恢复备份文件...\" >> ${DEPLOY_LOG}
                cp ${REMOTE_PATH}/api/server_bak ${REMOTE_PATH}/api/server
                chmod +x ${REMOTE_PATH}/api/server
            fi
            exit 1
        fi
    " || {
        printf "${RED}文件部署失败${NC}\n"
        return 1
    }
    
    printf "${GREEN}✓ 文件验证和部署成功${NC}\n"
    return 0
}

# 抽象步骤4：重启服务
restart_service() {
    printf "正在重启服务...\n"
    
    # 步骤1：执行重启操作
    sshpass -p "${REMOTE_PASS}" ssh "${REMOTE_USER}@${REMOTE_HOST}" "
        echo \"[\$(date '+%Y-%m-%d %H:%M:%S')] 开始检查进程...\" >> ${DEPLOY_LOG}
        
        # 检查进程是否存在（精确匹配我们的服务路径）
        if pgrep -f '${REMOTE_PATH}/api/server' > /dev/null 2>&1; then
            echo \"[\$(date '+%Y-%m-%d %H:%M:%S')] 发现已运行的服务，准备热重启...\" >> ${DEPLOY_LOG}
            
            # 发送SIGHUP信号进行热重启（精确匹配）
            pkill -HUP -f '${REMOTE_PATH}/api/server' 2>/dev/null
            echo \"[\$(date '+%Y-%m-%d %H:%M:%S')] SIGHUP信号已发送，等待重启...\" >> ${DEPLOY_LOG}
            sleep 5
            
            # 如果热重启失败，强制重启
            if ! pgrep -f '${REMOTE_PATH}/api/server' > /dev/null 2>&1; then
                echo \"[\$(date '+%Y-%m-%d %H:%M:%S')] 热重启失败，执行强制重启...\" >> ${DEPLOY_LOG}
                pkill -9 -f '${REMOTE_PATH}/api/server' 2>/dev/null
                sleep 2
                
                # 确保日志文件可写
                touch ${APP_LOG} ${ERROR_LOG}
                chmod 644 ${APP_LOG} ${ERROR_LOG}
                
                # 启动服务并重定向日志
                cd ${REMOTE_PATH}/api && nohup ${REMOTE_PATH}/api/server > ${APP_LOG} 2> ${ERROR_LOG} &
                echo \"[\$(date '+%Y-%m-%d %H:%M:%S')] 已启动新服务进程，日志文件: ${APP_LOG}, ${ERROR_LOG}\" >> ${DEPLOY_LOG}
            else
                echo \"[\$(date '+%Y-%m-%d %H:%M:%S')] 热重启成功\" >> ${DEPLOY_LOG}
            fi
        else
            echo \"[\$(date '+%Y-%m-%d %H:%M:%S')] 未发现运行中的服务，启动新服务...\" >> ${DEPLOY_LOG}
            
            # 清理可能存在的僵尸进程（精确匹配）
            pkill -9 -f '${REMOTE_PATH}/api/server' 2>/dev/null
            sleep 1
            
            # 确保日志文件可写
            touch ${APP_LOG} ${ERROR_LOG}
            chmod 644 ${APP_LOG} ${ERROR_LOG}
            
            # 启动服务并重定向日志
            cd ${REMOTE_PATH}/api && nohup ${REMOTE_PATH}/api/server > ${APP_LOG} 2> ${ERROR_LOG} &
            echo \"[\$(date '+%Y-%m-%d %H:%M:%S')] 已启动新服务进程，日志文件: ${APP_LOG}, ${ERROR_LOG}\" >> ${DEPLOY_LOG}
        fi
        
        echo \"[\$(date '+%Y-%m-%d %H:%M:%S')] 服务重启操作完成\" >> ${DEPLOY_LOG}
        echo '===========================================' >> ${DEPLOY_LOG}
    "
    
    # 步骤2：等待服务启动
    printf "等待服务启动...\n"
    sleep 2
    
    # 步骤3：验证服务状态（简化检查）
    printf "验证服务状态...\n"
    local service_check_count=0
    local max_checks=3
    
    while [ $service_check_count -lt $max_checks ]; do
        service_check_count=$((service_check_count + 1))
        printf "第 ${service_check_count} 次检查服务状态...\n"
        
        # 检查服务是否运行（精确匹配我们的服务路径）
        local is_running=$(sshpass -p "${REMOTE_PASS}" ssh "${REMOTE_USER}@${REMOTE_HOST}" "
            if pgrep -f '${REMOTE_PATH}/api/server' > /dev/null 2>&1; then
                echo 'YES'
            else
                echo 'NO'
            fi
        " 2>/dev/null)
        
        if [ "$is_running" = "YES" ]; then
            printf "${GREEN}✓ 服务运行状态确认成功${NC}\n"
            
            # 记录成功日志
            sshpass -p "${REMOTE_PASS}" ssh "${REMOTE_USER}@${REMOTE_HOST}" "
                echo \"[\$(date '+%Y-%m-%d %H:%M:%S')] 最终确认：服务运行正常\" >> ${DEPLOY_LOG}
            " 2>/dev/null
            
            return 0
        fi
        
        # 如果不是最后一次检查，等待一下再试
        if [ $service_check_count -lt $max_checks ]; then
            printf "${YELLOW}服务尚未就绪，等待 3 秒后重试...${NC}\n"
            sleep 3
        fi
    done
    
    # 所有检查都失败了
    printf "${YELLOW}⚠ 无法确认服务状态，但部署操作已完成${NC}\n"
    printf "${YELLOW}建议手动检查服务是否正常运行${NC}\n"
    
    # 记录警告日志
    sshpass -p "${REMOTE_PASS}" ssh "${REMOTE_USER}@${REMOTE_HOST}" "
        echo \"[\$(date '+%Y-%m-%d %H:%M:%S')] 警告：无法通过脚本确认服务状态，请手动检查\" >> ${DEPLOY_LOG}
    " 2>/dev/null
    
    # 因为用户说实际是成功的，所以这里返回成功但带警告
    return 0
}

# 模板方法：主部署流程
main_deploy_process() {
    printf "${GREEN}===========================================${NC}\n"
    printf "${GREEN}开始安全部署流程（包含文件完整性验证）${NC}\n"
    printf "${GREEN}===========================================${NC}\n"
    
    # 步骤1：构建程序
    if ! build_program; then
        printf "${RED}构建阶段失败，停止部署${NC}\n"
        exit 1
    fi
    
    # 步骤2：备份和上传
    if ! backup_and_upload; then
        printf "${RED}备份和上传阶段失败，停止部署${NC}\n"
        exit 1
    fi
    
    # 步骤3：验证并部署（关键的安全检查）
    if ! validate_and_deploy; then
        printf "${RED}验证和部署阶段失败，停止部署${NC}\n"
        printf "${YELLOW}服务保持现有状态，未进行重启${NC}\n"
        exit 1
    fi
    
    # 步骤4：重启服务
    if ! restart_service; then
        printf "${RED}服务重启失败${NC}\n"
        exit 1
    fi
    
    printf "${GREEN}===========================================${NC}\n"
    printf "${GREEN}✓ 安全部署流程完成${NC}\n"
    printf "${GREEN}===========================================${NC}\n"
    printf "${GREEN}日志文件位置：${NC}\n"
    printf "  - 部署日志: ${DEPLOY_LOG}\n"
    printf "  - 应用日志: ${APP_LOG}\n"
    printf "  - 错误日志: ${ERROR_LOG}\n"
}

# 执行主部署流程
main_deploy_process