#!/bin/bash

# 定义颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 检查 sshpass 是否已安装
if ! command -v sshpass &> /dev/null; then
    printf "${RED}错误: sshpass 未安装${NC}\n"
    printf "请运行以下命令安装 sshpass:\n"
    printf "  brew install esolitos/ipa/sshpass\n"
    exit 1
fi

# 定义变量
REMOTE_HOST="**************"
REMOTE_USER="root"
REMOTE_PASS="bNAqne8nfSU6DM"
# 如果环境变量 WEBPROJECT_PATH 存在就使用它，否则使用默认值
WEB_PATH=${WEBPROJECT_PATH:-"/Users/<USER>/lang/go/src/coding/daao/gin-vue-admin/web"}
REMOTE_PATH="/www/wwwroot/1fa.com.cn"
LOG_FILE="${REMOTE_PATH}/web/deploy.log"

printf "${GREEN}开始构建和打包前端...${NC}\n"

# 步骤1: 切换到Web项目目录
cd ${WEB_PATH} || {
    printf "${RED}切换目录失败: ${WEB_PATH}${NC}\n"
    exit 1
}
printf "✓ 已切换到项目目录\n"

# 步骤2: 加载和切换Node版本
printf "正在切换Node版本...\n"
# 加载 NVM
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # 加载 nvm
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # 加载自动完成

# 切换 Node 版本
nvm use 20.11.1 || {
    printf "${RED}Node版本切换失败${NC}\n"
    exit 1
}
printf "✓ Node版本切换成功\n"

# 步骤3: 构建项目
printf "正在构建项目...\n"
npm run build || {
    printf "${RED}项目构建失败${NC}\n"
    exit 1
}
printf "${GREEN}✓ 项目构建成功${NC}\n"

# 步骤4-5: 打包dist目录
printf "正在打包dist目录...\n"
cd dist || {
    printf "${RED}切换到dist目录失败${NC}\n"
    exit 1
}

# 删除已存在的 dist.zip
rm -f dist.zip

# 重新打包
zip -r dist.zip * || {
    printf "${RED}打包失败${NC}\n"
    exit 1
}

# 验证新打包的文件
printf "验证打包文件...\n"
if [ ! -f dist.zip ]; then
    printf "${RED}打包文件不存在${NC}\n"
    exit 1
fi
printf "新打包文件大小: $(ls -lh dist.zip | awk '{print $5}')\n"

# 步骤6-8: 备份并上传文件
printf "正在备份并上传文件到服务器...\n"
sshpass -p "${REMOTE_PASS}" ssh "${REMOTE_USER}@${REMOTE_HOST}" "
    # 确保目录存在
    mkdir -p ${REMOTE_PATH}
    
    # 备份现有文件
    if [ -f ${REMOTE_PATH}/dist.zip ]; then
        echo '原文件大小: '\$(ls -lh ${REMOTE_PATH}/dist.zip | awk '{print $5}') >> ${LOG_FILE}
        mv ${REMOTE_PATH}/dist.zip ${REMOTE_PATH}/dist.zip.bak
        echo '已备份原dist.zip文件' >> ${LOG_FILE}
    fi
"

# 上传新文件
printf "正在上传新文件...\n"
sshpass -p "${REMOTE_PASS}" scp -q dist.zip "${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH}/" || {
    printf "${RED}文件上传失败${NC}\n"
    exit 1
}

# 验证上传的文件
printf "验证上传文件...\n"
sshpass -p "${REMOTE_PASS}" ssh "${REMOTE_USER}@${REMOTE_HOST}" "
    if [ ! -f ${REMOTE_PATH}/dist.zip ]; then
        echo '错误：上传文件不存在' >> ${LOG_FILE}
        exit 1
    fi
    echo '新上传文件大小: '\$(ls -lh ${REMOTE_PATH}/dist.zip | awk '{print $5}') >> ${LOG_FILE}
"

# 解压文件
printf "正在解压文件...\n"
sshpass -p "${REMOTE_PASS}" ssh "${REMOTE_USER}@${REMOTE_HOST}" "
    cd ${REMOTE_PATH} && \
    # 清理旧文件
    rm -rf assets index.html favicon.ico logo.png && \
    echo '清理旧文件完成' >> ${LOG_FILE} && \
    # 解压新文件
    unzip -o dist.zip && \
    # 设置权限
    chmod -R 755 . && \
    chown -R www:www . && \
    echo '已完成文件解压和权限设置: '\$(date) >> ${LOG_FILE}
"

printf "${GREEN}✓ 部署完成${NC}\n"