#!/bin/bash

# 定义颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 检查 sshpass 是否已安装
if ! command -v sshpass &> /dev/null; then
    printf "${RED}错误: sshpass 未安装${NC}\n"
    printf "请运行以下命令安装 sshpass:\n"
    printf "  brew install esolitos/ipa/sshpass\n"
    exit 1
fi

# 定义变量
REMOTE_HOST="**************"
REMOTE_USER="root"
REMOTE_PASS="cwAgIvTHue2ak5E8"
LOCAL_PATH="/Users/<USER>/lang/python/social-auto-upload"
REMOTE_PATH="/www/social-auto-upload"
TEMP_UPLOAD_PATH="/tmp/moreapi_upload"

printf "${GREEN}开始检查并上传修改的文件...${NC}\n"

# 步骤1: 切换到本地项目目录
cd "${LOCAL_PATH}" || {
    printf "${RED}切换目录失败: ${LOCAL_PATH}${NC}\n"
    exit 1
}
printf "✓ 已切换到项目目录\n"

# 步骤2: 使用git获取修改过的文件列表
printf "正在检查修改过的文件...\n"
MODIFIED_FILES=$(git status --porcelain | grep -E '^(M| M|\?\?)' | awk '{print $2}')

if [ -z "$MODIFIED_FILES" ]; then
    printf "${RED}没有检测到修改过的文件${NC}\n"
    exit 0
fi

# 计算修改文件数量
MODIFIED_COUNT=$(echo "$MODIFIED_FILES" | wc -l)
printf "${GREEN}检测到 ${MODIFIED_COUNT} 个修改的文件${NC}\n"

# 步骤3: 创建临时上传目录并上传文件
printf "开始上传修改过的文件...\n"

# 在远程服务器创建临时上传目录
sshpass -p "${REMOTE_PASS}" ssh "${REMOTE_USER}@${REMOTE_HOST}" "mkdir -p ${TEMP_UPLOAD_PATH}" < /dev/null

echo "$MODIFIED_FILES" | while read -r file; do
    # 检查文件是否存在
    if [ -f "$file" ]; then
        # 计算临时上传路径
        temp_file_path="${TEMP_UPLOAD_PATH}/${file}"
        # 创建临时目录结构
        temp_dir=$(dirname "$temp_file_path")
        
        printf "上传: ${file} -> 临时目录\n"
        
        # 在远程服务器上创建临时目录结构
        sshpass -p "${REMOTE_PASS}" ssh "${REMOTE_USER}@${REMOTE_HOST}" "mkdir -p '${temp_dir}'" < /dev/null
        
        # 上传文件到临时目录
        sshpass -p "${REMOTE_PASS}" scp -q "${file}" "${REMOTE_USER}@${REMOTE_HOST}:${temp_file_path}" || {
            printf "${RED}文件 ${file} 上传失败${NC}\n"
            continue
        }
        
        # 计算最终目标路径
        final_file_path="${REMOTE_PATH}/${file}"
        final_dir=$(dirname "$final_file_path")
        
        # 使用root权限切换到www用户并移动文件
        sshpass -p "${REMOTE_PASS}" ssh "${REMOTE_USER}@${REMOTE_HOST}" "
            # 确保目标目录存在且属于www用户
            mkdir -p '${final_dir}' && chown www:www '${final_dir}'
            # 切换到www用户并移动文件
            sudo -u www cp '${temp_file_path}' '${final_file_path}'
            # 删除临时文件
            rm -f '${temp_file_path}'
        " < /dev/null || {
            printf "${RED}文件 ${file} 移动到最终位置失败${NC}\n"
            continue
        }
        
        printf "✓ 文件 ${file} 已以www用户身份上传成功\n"
    else
        printf "${RED}文件 ${file} 不存在或已删除${NC}\n"
    fi
done

# 清理临时上传目录
sshpass -p "${REMOTE_PASS}" ssh "${REMOTE_USER}@${REMOTE_HOST}" "rm -rf ${TEMP_UPLOAD_PATH}" < /dev/null

printf "${GREEN}✓ 所有修改过的文件上传完成${NC}\n"
