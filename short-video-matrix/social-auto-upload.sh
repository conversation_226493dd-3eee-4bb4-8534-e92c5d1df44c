#!/bin/bash

# 定义颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 检查 sshpass 是否已安装
if ! command -v sshpass &> /dev/null; then
    printf "${RED}错误: sshpass 未安装${NC}\n"
    printf "请运行以下命令安装 sshpass:\n"
    printf "  brew install esolitos/ipa/sshpass\n"
    exit 1
fi

# 定义变量
REMOTE_HOST="**************"
REMOTE_USER="root"
REMOTE_PASS="cwAgIvTHue2ak5E8"
LOCAL_PATH="/Users/<USER>/lang/python/social-auto-upload"
REMOTE_PATH="/www/social-auto-upload"
TEMP_UPLOAD_PATH="/tmp/moreapi_upload"

# SSH连接复用配置
SSH_CONTROL_PATH="/tmp/ssh_control_%h_%p_%r"
SSH_OPTS="-o ControlMaster=auto -o ControlPath=${SSH_CONTROL_PATH} -o ControlPersist=10m"

printf "${GREEN}开始检查并上传修改的文件...${NC}\n"

# 步骤1: 切换到本地项目目录
cd "${LOCAL_PATH}" || {
    printf "${RED}切换目录失败: ${LOCAL_PATH}${NC}\n"
    exit 1
}
printf "✓ 已切换到项目目录\n"

# 步骤2: 使用git获取修改过的文件列表
printf "正在检查修改过的文件...\n"
MODIFIED_FILES=$(git status --porcelain | grep -E '^(M| M|\?\?)' | awk '{print $2}')

if [ -z "$MODIFIED_FILES" ]; then
    printf "${RED}没有检测到修改过的文件${NC}\n"
    exit 0
fi

# 计算修改文件数量
MODIFIED_COUNT=$(echo "$MODIFIED_FILES" | wc -l)
printf "${GREEN}检测到 ${MODIFIED_COUNT} 个修改的文件${NC}\n"

# 步骤3: 建立SSH连接复用
printf "建立SSH连接复用...\n"
sshpass -p "${REMOTE_PASS}" ssh ${SSH_OPTS} "${REMOTE_USER}@${REMOTE_HOST}" "echo 'SSH连接已建立'" < /dev/null

# 步骤4: 创建临时上传目录并批量上传文件
printf "开始批量上传修改过的文件...\n"

# 在远程服务器创建临时上传目录
sshpass -p "${REMOTE_PASS}" ssh ${SSH_OPTS} "${REMOTE_USER}@${REMOTE_HOST}" "mkdir -p ${TEMP_UPLOAD_PATH}" < /dev/null

# 创建文件列表用于批量操作
UPLOAD_LIST=""
VALID_FILES=""

# 预处理文件列表，只保留存在的文件
echo "$MODIFIED_FILES" | while read -r file; do
    if [ -f "$file" ]; then
        echo "$file"
    else
        printf "${RED}文件 ${file} 不存在或已删除，跳过${NC}\n"
    fi
done > /tmp/valid_files.txt

# 读取有效文件列表
VALID_FILES=$(cat /tmp/valid_files.txt)
rm -f /tmp/valid_files.txt

if [ -z "$VALID_FILES" ]; then
    printf "${RED}没有有效的文件需要上传${NC}\n"
    exit 0
fi

# 批量创建远程目录结构
printf "创建远程目录结构...\n"
REMOTE_DIRS=""
echo "$VALID_FILES" | while read -r file; do
    temp_dir=$(dirname "${TEMP_UPLOAD_PATH}/${file}")
    final_dir=$(dirname "${REMOTE_PATH}/${file}")
    echo "mkdir -p '${temp_dir}' '${final_dir}' && chown www:www '${final_dir}'"
done | sort -u > /tmp/mkdir_commands.txt

sshpass -p "${REMOTE_PASS}" ssh ${SSH_OPTS} "${REMOTE_USER}@${REMOTE_HOST}" "$(cat /tmp/mkdir_commands.txt | tr '\n' ';')" < /dev/null
rm -f /tmp/mkdir_commands.txt

# 批量上传文件
printf "批量上传文件...\n"
echo "$VALID_FILES" | while read -r file; do
    temp_file_path="${TEMP_UPLOAD_PATH}/${file}"
    printf "上传: ${file}\n"

    sshpass -p "${REMOTE_PASS}" scp ${SSH_OPTS} -q "${file}" "${REMOTE_USER}@${REMOTE_HOST}:${temp_file_path}" || {
        printf "${RED}文件 ${file} 上传失败${NC}\n"
        continue
    }
done

# 批量移动文件到最终位置
printf "批量移动文件到最终位置...\n"
MOVE_COMMANDS=""
echo "$VALID_FILES" | while read -r file; do
    temp_file_path="${TEMP_UPLOAD_PATH}/${file}"
    final_file_path="${REMOTE_PATH}/${file}"
    echo "sudo -u www cp '${temp_file_path}' '${final_file_path}' && rm -f '${temp_file_path}'"
done > /tmp/move_commands.txt

sshpass -p "${REMOTE_PASS}" ssh ${SSH_OPTS} "${REMOTE_USER}@${REMOTE_HOST}" "$(cat /tmp/move_commands.txt | tr '\n' ';')" < /dev/null || {
    printf "${RED}部分文件移动失败${NC}\n"
}
rm -f /tmp/move_commands.txt

# 验证上传结果
printf "验证上传结果...\n"
echo "$VALID_FILES" | while read -r file; do
    final_file_path="${REMOTE_PATH}/${file}"
    if sshpass -p "${REMOTE_PASS}" ssh ${SSH_OPTS} "${REMOTE_USER}@${REMOTE_HOST}" "test -f '${final_file_path}'" < /dev/null; then
        printf "✓ 文件 ${file} 上传成功\n"
    else
        printf "${RED}✗ 文件 ${file} 上传失败${NC}\n"
    fi
done

# 清理临时上传目录
sshpass -p "${REMOTE_PASS}" ssh ${SSH_OPTS} "${REMOTE_USER}@${REMOTE_HOST}" "rm -rf ${TEMP_UPLOAD_PATH}" < /dev/null

printf "${GREEN}✓ 所有修改过的文件上传完成${NC}\n"
