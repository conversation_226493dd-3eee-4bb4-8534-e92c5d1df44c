#!/usr/bin/env bash

cd `dirname $0`
SHELL_DIR=`pwd`
HANDLE=$1
LOGS_DIR="$SHELL_DIR/logs"

if [ ! -d $LOGS_DIR ];then
  mkdir $LOGS_DIR
fi


if [[ $HANDLE != "start" && $HANDLE != "stop" ]]; then
	echo "error handle"
	echo "must be start or stop"
fi

if [[ $HANDLE == "start" ]]; then
	echo "start nats-streaming-server..."
	cd /Users/<USER>/program-soft/nats-streaming-server
	nohup ./nats-streaming -c config.conf > ${LOGS_DIR}/nats-streaming-server.log 2>&1 &
	if [[ $? -eq 0 ]]; then
		echo "successfully start nats-streaming-server"
	else
		echo "start nats-streaming-server failed"
	fi
	

	echo "start elasticsearch-head..."
	cd /Users/<USER>/program-soft/elasticsearch-head
	nohup npm run start > ${LOGS_DIR}/elasticsearch-head.log 2>&1 &
	if [[ $? -eq 0 ]]; then
		echo "successfully start elasticsearch-head"
	else
		echo "start elasticsearch-head failed"
	fi

	echo "start elasticsearch..."
	cd /Users/<USER>/program-soft/elasticsearch-8.1.2/bin
	nohup ./elasticsearch > ${LOGS_DIR}/elasticsearch.log 2>&1 &
	if [[ $? -eq 0 ]]; then
		echo "successfully start elasticsearch"
	else
		echo "start elasticsearch failed"
	fi

	echo "start kibana..."
	brew services start kibana-full > ${LOGS_DIR}/kibana.log 2>&1 &
	if [[ $? -eq 0 ]]; then
		echo "successfully start kibana"
	else
		echo "start kibana failed"
	fi
fi

if [[ $HANDLE == "stop" ]]; then
	kill -INT `pgrep nats-streaming-server`
	if [[ $? -eq 0 ]]; then
		echo "successfully stop nats-streaming-server"
	else
		echo "stop nats-streaming-server failed"
	fi

	kill -INT `lsof -i:9100 -t`
	if [[ $? -eq 0 ]]; then
		echo "successfully stop elasticsearch-head"
	else
		echo "stop elasticsearch-head failed"
	fi

	kill -INT `lsof -i:9200 -t`
	if [[ $? -eq 0 ]]; then
		echo "successfully stop elasticsearch"
	else
		echo "stop elasticsearch failed"
	fi

	brew services stop kibana-full
	if [[ $? -eq 0 ]]; then
		echo "successfully stop kibana"
	else
		echo "stop kibana failed"
	fi
fi